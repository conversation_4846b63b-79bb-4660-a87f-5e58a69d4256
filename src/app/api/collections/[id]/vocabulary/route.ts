import { NotFoundError, UnauthorizedError, ValidationError } from '@/backend/errors';
import { getWordService } from '@/backend/wire';
import { auth } from '@/lib';
import { WordDetail } from '@/models';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
	try {
		const session = await auth();
		const userId = session?.user?.id;

		if (!userId) {
			throw new UnauthorizedError(
				'Unauthorized: User must be authenticated to fetch words by collection.'
			);
		}

		const resolvedParams = await params;
		const collectionId = resolvedParams.id;
		if (!collectionId) {
			throw new ValidationError('Collection ID is required to fetch words.');
		}

		const wordService = getWordService();
		const words = await wordService.getWordsByCollection(userId, collectionId);

		return NextResponse.json(words as WordDetail[]);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		if (error instanceof NotFoundError) {
			return NextResponse.json({ error: error.message }, { status: 404 });
		}

		console.error(`Failed to fetch words for collection ${resolvedParams.id}:`, error);
		return NextResponse.json(
			{ error: 'Failed to fetch words by collection. Please try again.' },
			{ status: 500 }
		);
	}
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
	try {
		const session = await auth();
		const userId = session?.user?.id;

		if (!userId) {
			throw new UnauthorizedError(
				'Unauthorized: User must be authenticated to delete words from collection.'
			);
		}

		const resolvedParams = await params;
		const collectionId = resolvedParams.id;
		if (!collectionId) {
			throw new ValidationError('Collection ID is required to delete words.');
		}

		const body = await request.json();
		const { wordIds } = body;

		if (!Array.isArray(wordIds) || wordIds.length === 0) {
			throw new ValidationError('Word IDs array is required and cannot be empty.');
		}

		if (wordIds.length > 100) {
			throw new ValidationError('Cannot delete more than 100 words at a time.');
		}

		const wordService = getWordService();
		const deletedCount = await wordService.bulkDeleteWordsFromCollection(
			userId,
			collectionId,
			wordIds
		);

		return NextResponse.json({ deletedCount });
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		if (error instanceof NotFoundError) {
			return NextResponse.json({ error: error.message }, { status: 404 });
		}

		console.error(`Failed to bulk delete words from collection ${resolvedParams.id}:`, error);
		return NextResponse.json(
			{ error: 'Failed to delete words from collection. Please try again.' },
			{ status: 500 }
		);
	}
}
